self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"100e96b06c8278ca6faf6e44d8c371b8ce346c96\": {\n      \"workers\": {\n        \"app/signin/page\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cactions%5C%5Cauth.js%22%2C%5B%22signUp%22%2C%22signIn%22%2C%22signOut%22%2C%22signInWithGoogle%22%2C%22getSession%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/signin/page\": \"action-browser\"\n      }\n    },\n    \"4e6be9ee9f0542dc39cd8e460ca7d4cdc60da52d\": {\n      \"workers\": {\n        \"app/signin/page\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cactions%5C%5Cauth.js%22%2C%5B%22signUp%22%2C%22signIn%22%2C%22signOut%22%2C%22signInWithGoogle%22%2C%22getSession%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/signin/page\": \"action-browser\"\n      }\n    },\n    \"868ee57fd7a45dab546208a8523166f5b12095a9\": {\n      \"workers\": {\n        \"app/signin/page\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cactions%5C%5Cauth.js%22%2C%5B%22signUp%22%2C%22signIn%22%2C%22signOut%22%2C%22signInWithGoogle%22%2C%22getSession%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/signin/page\": \"action-browser\"\n      }\n    },\n    \"893b889b89c922f8cb97008b79071edd93013cb0\": {\n      \"workers\": {\n        \"app/signin/page\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cactions%5C%5Cauth.js%22%2C%5B%22signUp%22%2C%22signIn%22%2C%22signOut%22%2C%22signInWithGoogle%22%2C%22getSession%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/signin/page\": \"action-browser\"\n      }\n    },\n    \"b2070f0eaf348274dc78def4ff34d7b9d5db739a\": {\n      \"workers\": {\n        \"app/signin/page\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cactions%5C%5Cauth.js%22%2C%5B%22signUp%22%2C%22signIn%22%2C%22signOut%22%2C%22signInWithGoogle%22%2C%22getSession%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/signin/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"ibYrVE8RWEyJjq7AIoRmQhIcZzCUFnlj6mv8xmmuo4o=\"\n}"